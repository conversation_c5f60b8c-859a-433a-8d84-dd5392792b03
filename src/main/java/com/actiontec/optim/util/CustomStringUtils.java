package com.actiontec.optim.util;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import static com.actiontec.optim.platform.constant.ApplicationConstants.STRING_EMPTY;

public class CustomStringUtils extends StringUtils {
    public static String SPACE  = " ";

    public static String toStringOrNull(Object obj) {
        return obj != null ? String.valueOf(obj) : null;
    }

    public static String toBatchEquipmentFileEntry(String fileId) {
        return fileId != null ? ApplicationConstants.PUBLIC_FILE_PATH + fileId : null;
    }

    public static String convertObjectToString(Object object) {
        ObjectMapper objectMapper = new ObjectMapper();
        if (ObjectUtils.isEmpty(object)) {
            return STRING_EMPTY;
        }

        try {
            if (object instanceof String) {
                JsonNode jsonNode = objectMapper.readTree((String) object);
                return objectMapper.writeValueAsString(jsonNode);

            } else {
                return objectMapper.conv;
            }
        } catch (Exception e) {

        }

    }
}
