package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.api.v6.enums.*;
import com.actiontec.optim.platform.api.v6.handler.RpcResponseHandler;
import com.actiontec.optim.platform.api.v6.mapper.RttySessionMapper;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.util.LinuxAccountUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.SimpleRpcService;
import com.incs83.app.business.v2.SystemConfigService;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.*;
import com.incs83.app.responsedto.v2.System.ConfigPropertyDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ApplicationConstants.MAX_EXPIRED_HOURS;

/**
 * Remote Debug Session Service
 */
@Service
public class RemoteDebugService {

    private static final Logger logger = LogManager.getLogger(RemoteDebugService.class);

    @Autowired
    private AuditService auditService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private NCSEquipmentService ncsEquipmentService;

    @Autowired
    private RttyServerConfigService rttyServerConfigService;

    @Autowired
    private RttySessionService rttySessionService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SimpleRpcService cpeRpcService;

    @Autowired
    RttySessionMapper rttySessionMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Get all debug sessions
     * @param queryDTO Query parameters
     * @return Paginated list of sessions
     * @throws Exception If query fails
     */
    public PaginationResponse<RttySessionDTO> getAllSessions(RttySessionQueryDTO queryDTO) throws Exception {
        checkQueryParameter(queryDTO);
        return rttySessionService.getAllSessions(queryDTO);
    }

    /**
     * Create a new remote debug session
     * @param sessionRequest Session request containing serial number and duration
     * @return RemoteDebugSessionResponse containing session details
     * @throws Exception If creation fails
     */
    public RttySessionInitResponse createSession(RttySessionInitRequest sessionRequest) throws Exception {
        List<ConfigPropertyDTO> remoteDebugConfig;
        String maxSessions;
        String maxJoiners;
        // Validate login rtty permission
        if (!checkRttySessionPermission(ResourceName.DEBUG_SESSION.name(), PermissionTypeName.CREATE.name())) {
            logger.error("This role has no create permission.");
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        // Validate request parameters
        if (StringUtils.isEmpty(sessionRequest.getSerialNumber())) {
            logger.error("Serial number cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial number cannot be empty");
        }

        if (ObjectUtils.isEmpty(sessionRequest.getDurationHours())) {
            logger.error("Duration cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Duration cannot be empty");
        } else if (sessionRequest.getDurationHours() > MAX_EXPIRED_HOURS) {
            logger.error("Duration cannot be greater than 72 hours");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Duration cannot be greater than 72 hours");
        }

        // Step 1: Check equipment serial
        List<NCSEquipment> equipmentList = ncsEquipmentService.getNCSEquipmentListBySerialList(Collections.singletonList(sessionRequest.getSerialNumber()));
        if (CollectionUtils.isEmpty(equipmentList)) {
            logger.error("Equipment not found with serial number: " + sessionRequest.getSerialNumber());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment not found with serial number: " + sessionRequest.getSerialNumber());
        } else if (equipmentList.size() > 1) {
            logger.error("Multiple equipment found with serial number: " + sessionRequest.getSerialNumber());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Multiple equipment found with serial number: " + sessionRequest.getSerialNumber());
        }

        NCSEquipment ncsEquipment = equipmentList.get(0);

        // Step 2: Check if session already exists, if session is expired, delete it
        RttySession existingSession = rttySessionService.getSessionByEquipmentId(ncsEquipment.getId());
        if (!ObjectUtils.isEmpty(existingSession)) {
            if (existingSession.getExpiredAt().isBefore(LocalDateTime.now())) {
                rttySessionService.deleteSession(existingSession.getId());
            } else {
                logger.error("Session already exists for equipment with serial number: " + sessionRequest.getSerialNumber());
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Session already exists for equipment with serial number: " + sessionRequest.getSerialNumber());
            }
        }

        // Step 3: Check RTTY server configuration
        RttyServerConfig rttyServerConfig = rttyServerConfigService.getServerConfig();
        if (ObjectUtils.isEmpty(rttyServerConfig)) {
            logger.error("No RTTY server config found");
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No RTTY server config found");
        }

        // Step 4: Check if max sessions limit is reached
        // according trs, max sessions default value is 1k, max joiners default value is 4
        remoteDebugConfig = getRemoteDebugConfig();
        maxSessions = remoteDebugConfig.stream()
                .filter(config -> config.getProperty().equals(RttyServerSessionConfig.MAX_REMOTE_DEBUG_SESSIONS.name()))
                .findFirst()
                .map(ConfigPropertyDTO::getValue)
                .orElse("1000");
        rttySessionService.validateMaxSession(Integer.valueOf(maxSessions));

        maxJoiners = remoteDebugConfig.stream()
                .filter(config -> config.getProperty().equals(RttyServerSessionConfig.MAX_REMOTE_DEBUG_JOINERS_PER_SESSION.name()))
                .findFirst()
                .map(ConfigPropertyDTO::getValue)
                .orElse("4");

        // Step 5: Create RTTY session
        // Default duration is 24 hours
        Integer durationHours = sessionRequest.getDurationHours();
        RttySession newSession = rttySessionService.createSession(ncsEquipment, durationHours, rttyServerConfig);
        String userName = LinuxAccountUtils.generateLinuxUsername();
        String password = LinuxAccountUtils.generateLinuxPassword();

        // Step 6: Call RPC to notify CPE to create session
        String rpcUri = RpcConstants.REMOTE_DEBUG_RTTY_URI;
        try {
            String payload = objectMapper.writeValueAsString(rttySessionMapper.toRttyRpcInitSessionRequest(newSession, userName, password, rttyServerConfig, durationHours, Integer.valueOf(maxJoiners)));
            logger.info("create session payload: {}", payload);
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, ncsEquipment.getNetworkId(), ncsEquipment.getSerial(), rpcUri, HttpMethod.POST.name(), payload, ApplicationConstants.RPC_RTTY_SESSION_API_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
            RpcResponseHandler.handleCreateOrDeleteSessionRpcResponse(rpcResult, ncsEquipment);
        } catch (ValidationException createRpcException) {
            logger.error("send cpe create session failed. serial:{}, status: {}, exception:{}", ncsEquipment.getSerial(), createRpcException.getCode(),createRpcException.getMessage());
            cleanUpSession(newSession, ncsEquipment, rpcUri);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe create session failed.");
        } catch (Exception exception) {
            logger.error("send cpe create session waiting timeout or other exception. serial:{}, exception:{}", ncsEquipment.getSerial(), exception.getMessage());
            cleanUpSession(newSession, ncsEquipment, rpcUri);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe create session waiting timeout.");
        }

        newSession.setStatus(RttySessionStatus.ACTIVE);
        rttySessionService.updateSession(newSession);


        return rttySessionMapper.toRttySessionInitResponse(newSession, userName, password);
    }

    /**
     * Join or extend debug session
     * @param sessionId Session ID
     * @param actionRequest Join or extend request
     * @return RttySessionResponse containing session details
     * @throws Exception If update fails
     */
    public RttySessionActionResponse<?> joinOrExtendSession(String sessionId, RttySessionActionRequest actionRequest) throws Exception {
        RttySessionActionResponse<?> response;
        // Validate session ID
        if (CustomStringUtils.isEmpty(sessionId)) {
            logger.error("Session ID cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Session ID cannot be empty");
        }

        // Validate request parameters
        if (CustomStringUtils.isEmpty(actionRequest.getToken())) {
            logger.error("Token cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Token cannot be empty");
        }

        if (actionRequest.getType() == null) {
            logger.error("Type cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Type cannot be empty");
        }

        RttySession existingSession = rttySessionService.getSessionById(sessionId);
        RttySession updatedSession;

        if (ObjectUtils.isEmpty(existingSession)) {
            logger.error("Session not found with ID: " + sessionId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Session not found with ID: " + sessionId);
        }

        if (!StringUtils.equals(existingSession.getToken(), actionRequest.getToken())) {
            logger.error("Invalid token");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid token");
        }

        // Handle different operation types
        switch (actionRequest.getType()) {
            case Join:
                // Check if user has HTTP and SSH permissions
                boolean hasHttpPermission = checkRttySessionPermission(ResourceName.DEBUG_CONNECTION.name(), PermissionTypeName.HTTP.name());
                boolean hasSshPermission = checkRttySessionPermission(ResourceName.DEBUG_CONNECTION.name(), PermissionTypeName.SSH.name());

                // If user has neither permission, deny access
                if (!hasHttpPermission && !hasSshPermission) {
                    logger.error("This role has no http and ssh permission.");
                    throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
                }

                if (existingSession.getStatus() == RttySessionStatus.INITIAL) {
                    existingSession.setStatus(RttySessionStatus.ACTIVE);
                    rttySessionService.updateSession(existingSession);
                }

                // Create response based on user permissions
                response = new RttySessionActionResponse<>(RttySessionOperationType.Join, rttySessionMapper.toRttySessionJoinResponse(existingSession, hasHttpPermission, hasSshPermission));
                break;
            case Extend:
                // Verify if user has permission to extend the session
                if (!checkRttySessionPermission(ResourceName.DEBUG_SESSION.name(), PermissionTypeName.EXTEND.name())) {
                    logger.error("This role has no extend permission.");
                    throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
                }

                if (actionRequest.getExtendHours() == null || actionRequest.getExtendHours() <= 0) {
                    logger.error("Extend hours must be greater than 0");
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Extend hours must be greater than 0");
                }

                // Check if the session is active
                if (existingSession.getStatus() != RttySessionStatus.ACTIVE) {
                    logger.error("Cannot extend a session with status: " + existingSession.getStatus());
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot extend a session with status: " + existingSession.getStatus());
                }

                // Validate the new expiration time
                rttySessionService.validateSessionExpiredAt(existingSession, actionRequest.getExtendHours());

                // get equipment from existing session
                NCSEquipment ncsEquipment = ncsEquipmentService.getNCSEquipmentById(existingSession.getEquipmentId());
                ObjectMapper localMapper = objectMapper.copy();
                localMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                // Call RPC to notify CPE to extend session
                String rpcUri = RpcConstants.REMOTE_DEBUG_RTTY_URI;
                try {
                    String payload = objectMapper.writeValueAsString(rttySessionMapper.toRttyRpcExtendSessionRequest(actionRequest.getExtendHours()));
                    logger.info("extend session payload: {}", payload);
                    Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, ncsEquipment.getNetworkId(), ncsEquipment.getSerial(), rpcUri, HttpMethod.PUT.name(), payload, ApplicationConstants.RPC_RTTY_SESSION_API_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

                    // Handle RPC response
                    RpcResponseHandler.handleExtendSessionRpcResponse(rpcResult, ncsEquipment);
                    List<Map<String, Object>> rpcResponsePayloadList = (List<Map<String, Object>>) rpcResult.get("payload");
                    Map<String, Object> rpcResponsePayload = rpcResponsePayloadList.get(0);
                    RttyRpcResponse rttyRpcResponse = localMapper.convertValue(rpcResponsePayload, RttyRpcResponse.class);

                    // update session
                    existingSession.setExpiredAt(LocalDateTime.ofEpochSecond(rttyRpcResponse.getExpiredAt(), 0, ZoneOffset.UTC));
                    rttySessionService.updateSession(existingSession);
                } catch (ValidationException extendRpcException) {
                    logger.error("send cpe extend session failed. serial:{}, status: {}, exception:{}", ncsEquipment.getSerial(), extendRpcException.getCode(),extendRpcException.getMessage());
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe extend session failed.");
                } catch (Exception exception) {
                    logger.error("send cpe extend session waiting timeout or other exception. serial:{}, exception:{}", ncsEquipment.getSerial(), exception.getMessage());
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe extend session waiting timeout.");
                }

                updatedSession = rttySessionService.getSessionById(sessionId);
                response = new RttySessionActionResponse<>(RttySessionOperationType.Extend, rttySessionMapper.toRttySessionExtendResponse(updatedSession));
                break;
            default:
                logger.error("Invalid operation type");
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid operation type");
        }

        return response;
    }

    /**
     * Delete debug session
     * @param sessionId Session ID
     * @throws Exception If deletion fails
     */
    public void deleteSession(String sessionId) throws Exception {
        if (!checkRttySessionPermission(ResourceName.DEBUG_SESSION.name(), PermissionTypeName.DELETE.name())) {
            logger.error("This role has no delete permission.");
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        if (CustomStringUtils.isEmpty(sessionId)) {
            logger.error("Session ID cannot be empty");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Session ID cannot be empty");
        }

        RttySession existingSession = rttySessionService.getSessionById(sessionId);
        if (ObjectUtils.isEmpty(existingSession)) {
            logger.error("Session not found with ID: " + sessionId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Session not found with ID: " + sessionId);
        }

        // get equipment from existing session
        NCSEquipment ncsEquipment = ncsEquipmentService.getNCSEquipmentById(existingSession.getEquipmentId());

        // Delete session
        rttySessionService.deleteSession(sessionId);

        // Call RPC to notify CPE to delete session
        String rpcUri = RpcConstants.REMOTE_DEBUG_RTTY_URI;
        HashMap<String, Object> payloadMap = new HashMap<>();
        try {
            String payload = objectMapper.writeValueAsString(payloadMap);
            logger.info("delete payload: {}", payload);
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, ncsEquipment.getNetworkId(), ncsEquipment.getSerial(), rpcUri, RequestMethod.DELETE.name(), payload, ApplicationConstants.RPC_RTTY_SESSION_API_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

            // Handle RPC response
            RpcResponseHandler.handleCreateOrDeleteSessionRpcResponse(rpcResult, ncsEquipment);
        } catch (ValidationException deleteRpcException) {
            logger.error("send cpe delete session failed. serial:{}, status: {}, exception:{}", ncsEquipment.getSerial(), deleteRpcException.getCode(),deleteRpcException.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe delete session failed.");
        } catch (Exception exception) {
            logger.error("send cpe delete session waiting timeout or other exception. serial:{}, exception:{}", ncsEquipment.getSerial(), exception.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "send cpe delete session waiting timeout.");
        }
    }

    /**
     * Clean up session when call cpe api session failed
     * @param session
     * @param ncsEquipment
     * @param rpcUri
     */
    private void cleanUpSession(RttySession session, NCSEquipment ncsEquipment, String rpcUri) {
        logger.info("clean up session start!");
        try {
            // Error cases, need to remove delete session
            rttySessionService.deleteSession(session.getId());
            HashMap<String, Object> payloadMap = new HashMap<>();

            // Call RPC to notify CPE to delete session
            String deletePayload = objectMapper.writeValueAsString(payloadMap);
            // we don't handle return here, notify cpe to delete.
            logger.info("clean up session,notify cpe delete session, send rpc payload: {}", deletePayload);
            cpeRpcService.sendRpcAndWaitResult(null, ncsEquipment.getNetworkId(), ncsEquipment.getSerial(), rpcUri, RequestMethod.DELETE.name(), deletePayload, ApplicationConstants.RPC_RTTY_SESSION_API_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        } catch (ValidationException rpcException) {
            logger.error("clean up session, notify cpe delete session failed. serial:{}, exception:{}", ncsEquipment.getId(), rpcException.getMessage());
        } catch (Exception exception) {
            logger.error("clean up session unexpected exception: {}", exception.getMessage());
        }
    }

    /**
     * Get remote debug configuration properties
     * @return List of configuration properties for remote debugging
     */
    private List<ConfigPropertyDTO> getRemoteDebugConfig() {
        // Get enum names for filtering
        Set<String> configNames = Arrays.stream(RttyServerSessionConfig.values())
                .map(Enum::name)
                .collect(Collectors.toSet());

        // Get and filter properties
        return systemConfigService.getAllSystemConfig().stream()
                .filter(config -> SystemConfigTitle.DebugConfig.toString().equals(config.getTitle()))
                .flatMap(config -> config.getProps().stream())
                .filter(prop -> configNames.contains(prop.getProperty()))
                .collect(Collectors.toList());
    }

    /**
     * Check if current user has permission for specified resource and permission type
     * @param resourceName Resource name
     * @param permissionTypeName Permission type name
     * @throws Exception If user doesn't have permission or other errors occur
     */
    private boolean checkRttySessionPermission(String resourceName, String permissionTypeName) throws Exception {
        String roleId = CommonUtils.getRoleIdOfLoggedInUser();
        Role role = roleService.getRoleById(roleId);
        if (ObjectUtils.isEmpty(role)) {
            logger.error("Role not found for roleId: {}", roleId);
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        Set<RoleResourcePermission> roleResourcePermissions = role.getRolePermissions();
        if (ObjectUtils.isEmpty(roleResourcePermissions)) {
            logger.error("RoleResourcePermissions not found for roleId: {}", roleId);
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        // Check if user has permission for the specified resource and permission type
        return roleResourcePermissions.stream().anyMatch(permission ->
                    permission.getResource() != null &&
                    permission.getPermissionType() != null &&
                    CustomStringUtils.equals(permission.getResource().getName(), resourceName) &&
                    CustomStringUtils.equals(permission.getPermissionType().getName(), permissionTypeName)
                );
    }

    private void checkQueryParameter(RttySessionQueryDTO queryDTO) throws Exception {
        if (CustomStringUtils.isEmpty(queryDTO.getSerialNumber()) && CustomStringUtils.isEmpty(queryDTO.getCreatorName()) && !queryDTO.isCreatedByMe()) {
            logger.error("Either serial number or creator name must be provided.");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Either serial number or creator name must be provided.");
        }
    }
}

