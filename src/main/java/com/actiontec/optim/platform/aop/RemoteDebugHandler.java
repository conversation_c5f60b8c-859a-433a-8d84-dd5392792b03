package com.actiontec.optim.platform.aop;

import com.actiontec.optim.platform.annotation.RemoteDebugOperation;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.IntStream;

@Aspect
@Component
@Order(2)
public class RemoteDebugHandler {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private AuditService auditService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    ObjectMapper objectMapper;

    @Around("execution(* com.actiontec.optim.platform.api.v6.controller.RemoteDebugController.*(..))")
    public Object handleAuditLog(ProceedingJoinPoint joinPoint) throws Throwable {
        String operation;
        String payload;

        Object result = null;
        String statusCode = HttpStatus.OK.toString();
        String responseData = CustomStringUtils.EMPTY;

        try {
            operation = extractOperation(joinPoint);
            payload = extractPayload(joinPoint);


            result = joinPoint.proceed();
            if (CustomStringUtils.isJsonString(result)) {
                responseData = result.toString();
            } else {
                responseData = objectMapper.writeValueAsString(result);
            }
        } catch (ValidationException ve) {
            statusCode = String.valueOf(ve.getCode());
            responseData = ve.getMessage();
            throw ve;
        } catch (Exception e) {
            statusCode = HttpStatus.INTERNAL_SERVER_ERROR.toString();
            responseData = e.getMessage();
            throw e;
        } finally {
            try {
                logger.info("audit log: operation: {}, payload: {}, statusCode: {}, responseData: {}", operation, payload, statusCode, responseData);
                auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, operation, payload, statusCode, responseData, httpServletRequest);
            } catch (Exception auditException) {
                logger.error("Error in creating AuditLog for remote debug operaiont: {}. message: {}", operation, auditException.getMessage());
            }
        }
        return result;
    }

//    @AfterThrowing(pointcut = "execution(* com.actiontec.optim.platform.api.v6.controller.RemoteDebugController.*(..))", throwing = "ex")
//    public void handleValidateException(JoinPoint joinPoint, ValidationException ex) throws ValidationException {
//        try {
//            logger.error("validation exception payload: {}", objectMapper.writeValueAsString(extractPayload(joinPoint)));
//            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, extractOperation(joinPoint),
//                    extractPayload(joinPoint), String.valueOf(ex.getCode()), ex.getMessage(), httpServletRequest);
//        } catch (Exception auditException) {
//            logger.error("Error in creating AuditLog for remote debug.");
//        }
//        throw ex;
//    }
//
//    @AfterThrowing(pointcut = "execution(* com.actiontec.optim.platform.api.v6.controller.RemoteDebugController.*(..))", throwing = "ex")
//    public void handleException(JoinPoint joinPoint, Exception ex) throws Exception {
//        try {
//            logger.error("exception payload: {}", objectMapper.writeValueAsString(extractPayload(joinPoint)));
//            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, extractOperation(joinPoint),
//                    extractPayload(joinPoint), HttpStatus.INTERNAL_SERVER_ERROR.toString(), ex.getMessage(), httpServletRequest);
//        } catch (Exception auditException) {
//            logger.error("Error in creating AuditLog for remote debug.");
//        }
//        throw ex;
//    }

    private String extractPayload(JoinPoint joinPoint) throws JsonProcessingException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        // check if there is any argument annotated with @RequestBody or @ModelAttribute
        Optional<Object> payload = IntStream.range(0, args.length)
                .filter(i -> Arrays.stream(parameterAnnotations[i])
                        .anyMatch(annotation -> annotation instanceof RequestBody || annotation instanceof ModelAttribute))
                .mapToObj(i -> args[i])
                .findFirst();

        // if no argument is annotated with @RequestBody or @ModelAttribute, check if there is any argument annotated with @PathVariable
        if (!payload.isPresent()) {
            payload = IntStream.range(0, args.length)
                    .filter(i -> Arrays.stream(parameterAnnotations[i])
                            .anyMatch(annotation -> annotation instanceof PathVariable))
                    .mapToObj(i -> args[i])
                    .findFirst();
        }

        return CustomStringUtils.isJsonString(payload) ? (String) payload.get() : objectMapper.writeValueAsString(payload.get());
    }

    private String extractOperation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RemoteDebugOperation annotation = method.getAnnotation(RemoteDebugOperation.class);
        return !ObjectUtils.isEmpty(annotation) && CustomStringUtils.isNotBlank(annotation.operation())
                ? annotation.operation() : CustomStringUtils.EMPTY;
    }

}
